# Database Configuration (Docker)
DATABASE_URL=************************************/rental_mvp?sslmode=disable
DATABASE_HOST=db
DATABASE_PORT=5432
DATABASE_NAME=rental_mvp
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_SSL_MODE=disable
DATABASE_MAX_CONNECTIONS=25
DATABASE_MAX_IDLE_CONNECTIONS=10
DATABASE_MAX_LIFETIME_MINUTES=5

# Server Configuration
PORT=8080
HOST=0.0.0.0
ENVIRONMENT=development
CORS_ORIGINS=http://localhost:3000,http://frontend:3000
TRUSTED_PROXIES=*

# JWT Configuration
JWT_SECRET=dev-jwt-secret-not-for-production-use
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=168h

# Development Settings
LOG_LEVEL=debug
LOG_FORMAT=text
AUTO_MIGRATE=true
MIGRATIONS_PATH=./migrations

# Storage (local for development)
STORAGE_PROVIDER=local
LOCAL_STORAGE_PATH=./uploads