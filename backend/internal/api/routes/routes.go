package routes

import (
	"net/http"

	"github.com/go-chi/chi/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/nyunja/rentbase/backend/internal/api/handlers"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/config"
	"go.uber.org/zap"
)

func SetupRoutes(r chi.Router, db *pgxpool.Pool, cfg *config.Config, logger *zap.Logger) {
	// Initialize services
	jwtService := auth.NewJWTService(cfg.JWT.Secret, cfg.JWT.ExpiresIn)
	refreshTokenService := auth.NewRefreshTokenService(db, cfg.JWT.RefreshTokenExpiresIn)

	// Initialize middleware
	// authMiddleware := middleware.NewAuthMiddleware(jwtService)
	// rateLimiter := middleware.NewRateLimiter(60, 10)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db, jwtService, refreshTokenService, cfg, logger)

	// API v1 routes
	r.Route("/api/v1", func(r chi.Router) {
		// Health check (already defined in main.go, but can be here too)
		r.Get("/ping", func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{"message":"pong","service":"rental-property-mvp"}`))
		})

		// Public routes
		r.Route("/auth", func(r chi.Router) {
			r.Post("/signup", authHandler.Signup)
			r.Post("/login", authHandler.Login)
			r.Post("/refresh", authHandler.RefreshToken)
			r.Post("/logout", authHandler.Logout)
		})

		// Protected routes (to be implemented)
		r.Route("/properties", func(r chi.Router) {
			// Add auth middleware here
			r.Get("/", func(w http.ResponseWriter, r *http.Request) {
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusNotImplemented)
				w.Write([]byte(`{"message":"properties list - to be implemented"}`))
			})
		})
	})
}
