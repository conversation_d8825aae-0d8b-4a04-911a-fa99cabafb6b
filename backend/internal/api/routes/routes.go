package routes

import (
    "net/http"

    "github.com/go-chi/chi/v5"
    "github.com/jackc/pgx/v5/pgxpool"
    "github.com/nyunja/rentbase/backend/internal/config"
    "go.uber.org/zap"
)

func SetupRoutes(r chi.Router, db *pgxpool.Pool, cfg *config.Config, logger *zap.Logger) {
    // API v1 routes
    r.Route("/api/v1", func(r chi.Router) {
        // Health check (already defined in main.go, but can be here too)
        r.Get("/ping", func(w http.ResponseWriter, r *http.Request) {
            w.Header().Set("Content-Type", "application/json")
            w.WriteHeader(http.StatusOK)
            w.Write([]byte(`{"message":"pong","service":"rental-property-mvp"}`))
        })

        // Auth routes (to be implemented)
        r.Route("/auth", func(r chi.Router) {
            r.<PERSON>("/signup", func(w http.ResponseWriter, r *http.Request) {
                w.Header().Set("Content-Type", "application/json")
                w.WriteHeader(http.StatusNotImplemented)
                w.Write([]byte(`{"message":"signup endpoint - to be implemented"}`))
            })
            r.Post("/login", func(w http.ResponseWriter, r *http.Request) {
                w.Header().Set("Content-Type", "application/json")
                w.WriteHeader(http.StatusNotImplemented)
                w.Write([]byte(`{"message":"login endpoint - to be implemented"}`))
            })
        })

        // Protected routes (to be implemented)
        r.Route("/properties", func(r chi.Router) {
            // Add auth middleware here
            r.Get("/", func(w http.ResponseWriter, r *http.Request) {
                w.Header().Set("Content-Type", "application/json")
                w.WriteHeader(http.StatusNotImplemented)
                w.Write([]byte(`{"message":"properties list - to be implemented"}`))
            })
        })
    })
}