package handlers

import (
	"encoding/json"
	"net/http"

	"github.com/nyunja/rentbase/backend/internal/api/dto"
	"github.com/nyunja/rentbase/backend/internal/auth"
	"github.com/nyunja/rentbase/backend/internal/config"
	"github.com/nyunja/rentbase/backend/internal/utils"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

type AuthHandler struct {
	db                  *pgxpool.Pool
	jwtService          *auth.JWTService
	refreshTokenService *auth.RefreshTokenService
	config              *config.Config
	logger              *zap.Logger
}

func NewAuthHandler(db *pgxpool.Pool, jwtService *auth.JWTService, refreshTokenService *auth.RefreshTokenService, config *config.Config, logger *zap.Logger) *AuthHandler {
	return &AuthHandler{
		db:                  db,
		jwtService:          jwtService,
		refreshTokenService: refreshTokenService,
		config:              config,
		logger:              logger,
	}
}

func (h *AuthHandler) Signup(w http.ResponseWriter, r *http.Request) {
	var req dto.SignupRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Check if user already exists
	var existingUserID string
	err := h.db.QueryRow(r.Context(), "SELECT id FROM users WHERE email = $1", req.Email).Scan(&existingUserID)
	if err != pgx.ErrNoRows {
		if err == nil {
			utils.WriteErrorResponse(w, http.StatusConflict, "User with this email already exists")
			return
		}
		h.logger.Error("Database error checking existing user", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Hash password
	hashedPassword, err := auth.HashPassword(req.Password)
	if err != nil {
		h.logger.Error("Failed to hash password", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Create user
	var userID string
	query := `
        INSERT INTO users (email, password_hash, name)
        VALUES ($1, $2, $3)
        RETURNING id`

	err = h.db.QueryRow(r.Context(), query, req.Email, hashedPassword, req.Name).Scan(&userID)
	if err != nil {
		h.logger.Error("Failed to create user", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Failed to create user")
		return
	}

	// Generate tokens
	accessToken, err := h.jwtService.GenerateToken(userID, req.Email, req.Name)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	refreshToken, err := h.refreshTokenService.GenerateRefreshToken(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to generate refresh token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Set refresh token as HTTP-only cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    refreshToken,
		Path:     "/",
		MaxAge:   int(h.config.JWT.RefreshTokenExpiresIn.Seconds()),
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    userID,
			Email: req.Email,
			Name:  req.Name,
		},
	}

	utils.WriteSuccessResponse(w, response, "User created successfully")
}

func (h *AuthHandler) Login(w http.ResponseWriter, r *http.Request) {
	var req dto.LoginRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.WriteErrorResponse(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Validate request
	if errors := req.Validate(); len(errors) > 0 {
		utils.WriteJSONResponse(w, http.StatusBadRequest, map[string]interface{}{
			"error":  "Validation failed",
			"errors": errors,
		})
		return
	}

	// Get user from database
	var userID, hashedPassword, name string
	query := `SELECT id, password_hash, name FROM users WHERE email = $1`
	err := h.db.QueryRow(r.Context(), query, req.Email).Scan(&userID, &hashedPassword, &name)
	if err != nil {
		if err == pgx.ErrNoRows {
			utils.WriteErrorResponse(w, http.StatusUnauthorized, "Invalid email or password")
			return
		}
		h.logger.Error("Database error during login", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Check password
	if !auth.CheckPassword(req.Password, hashedPassword) {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "Invalid email or password")
		return
	}

	// Generate tokens
	accessToken, err := h.jwtService.GenerateToken(userID, req.Email, name)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	refreshToken, err := h.refreshTokenService.GenerateRefreshToken(r.Context(), userID)
	if err != nil {
		h.logger.Error("Failed to generate refresh token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Set refresh token as HTTP-only cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    refreshToken,
		Path:     "/",
		MaxAge:   int(h.config.JWT.RefreshTokenExpiresIn.Seconds()),
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    userID,
			Email: req.Email,
			Name:  name,
		},
	}

	utils.WriteSuccessResponse(w, response, "Login successful")
}

func (h *AuthHandler) RefreshToken(w http.ResponseWriter, r *http.Request) {
	// Get refresh token from cookie
	cookie, err := r.Cookie(h.config.JWT.RefreshTokenCookieName)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "Refresh token not found")
		return
	}

	// Validate refresh token
	userID, err := h.refreshTokenService.ValidateRefreshToken(r.Context(), cookie.Value)
	if err != nil {
		utils.WriteErrorResponse(w, http.StatusUnauthorized, "Invalid refresh token")
		return
	}

	// Get user info
	var email, name string
	query := `SELECT email, name FROM users WHERE id = $1`
	err = h.db.QueryRow(r.Context(), query, userID).Scan(&email, &name)
	if err != nil {
		h.logger.Error("Failed to get user info", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	// Generate new access token
	accessToken, err := h.jwtService.GenerateToken(userID, email, name)
	if err != nil {
		h.logger.Error("Failed to generate access token", zap.Error(err))
		utils.WriteErrorResponse(w, http.StatusInternalServerError, "Internal server error")
		return
	}

	response := dto.AuthResponse{
		AccessToken: accessToken,
		TokenType:   "Bearer",
		ExpiresIn:   int(h.config.JWT.ExpiresIn.Seconds()),
		User: dto.UserInfo{
			ID:    userID,
			Email: email,
			Name:  name,
		},
	}

	utils.WriteSuccessResponse(w, response, "Token refreshed successfully")
}

func (h *AuthHandler) Logout(w http.ResponseWriter, r *http.Request) {
	// Get refresh token from cookie
	cookie, err := r.Cookie(h.config.JWT.RefreshTokenCookieName)
	if err == nil {
		// Revoke refresh token
		_ = h.refreshTokenService.RevokeRefreshToken(r.Context(), cookie.Value)
	}

	// Clear refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.config.JWT.RefreshTokenCookieName,
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   h.config.Server.Environment == "production",
		SameSite: http.SameSiteLaxMode,
	})

	utils.WriteSuccessResponse(w, nil, "Logout successful")
}
